from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from webdriver_manager.microsoft import EdgeChromiumDriverManager
import os

# 你的 Cookie 字符串
cookie_str = "cf_clearance=BfAfNFUwX7oPVIw8D.PCC34AzK1PKA7CDUi45L8FkrM-1748527860-1.2.1.1-t9RSTYBx5NTG9ieEvMjFyPD8sZLp.DXyKjXyJaJtuCpPTQg_nN.VDeyYjndrS9eURRPog9MKnlL1d22yU6_02z9uBXKcQ_MkSNgWdrnQ7RGV4AJFQWdZDViFbDpHcBAnOPoOXHyhsCDgd__5Fl96n_jP9fuZu1ODlFb8pNsZHfRASv.ajLeHZQLurCQA60NUyxIs0QHADaHD35_.ED50gBfV5TuAzToU1hF6KnkFnIe465MbCtDOyWWKJcdup7EpenvT4Dic636YNDel9VT4FgwrtKO.VnS16JbeQkwnBYtjsNFu0vCjGhHZDUm4.vW3GKupWOcd5T2SaOSpWOQ9ResSsrB0QPzMgXga5hL5nOH.S2tgsvsQa1sCqq8yKIjN; c_secure_uid=MjE2NDc%3D; c_secure_pass=9e5a93ed444bd64e615d7213651e0bf0; c_secure_ssl=eWVhaA%3D%3D; c_secure_tracker_ssl=eWVhaA%3D%3D; c_secure_login=bm9wZQ%3D%3D"

def parse_cookie(cookie_str):
    cookies = []
    for item in cookie_str.split(';'):
        if '=' in item:
            name, value = item.strip().split('=', 1)
            cookies.append({'name': name, 'value': value, 'domain': 'audiences.me'})
    return cookies

# 配置 Edge 浏览器选项
options = Options()
options.add_argument("--disable-blink-features=AutomationControlled")
options.add_experimental_option("excludeSwitches", ["enable-automation"])
options.add_experimental_option('useAutomationExtension', False)

# 启动 Edge 浏览器
def setup_edge_driver():
    """设置 Edge WebDriver"""
    try:
        service = Service(EdgeChromiumDriverManager().install())
        print("✅ 成功自动下载 Edge 驱动")
        return service
    except Exception as e:
        print(f"⚠️  自动下载 Edge 驱动失败: {e}")
        print("🔄 尝试使用本地驱动...")

        # 创建 drivers 目录（如果不存在）
        drivers_dir = os.path.join(os.path.dirname(__file__), "drivers")
        os.makedirs(drivers_dir, exist_ok=True)

        driver_path = os.path.join(drivers_dir, "msedgedriver.exe")
        if os.path.exists(driver_path):
            service = Service(executable_path=driver_path)
            print(f"✅ 使用本地驱动: {driver_path}")
            return service
        else:
            print(f"❌ 本地驱动不存在: {driver_path}")
            print("\n📥 请按以下步骤手动下载 Edge WebDriver:")
            print("1. 打开 Edge 浏览器，访问 edge://version/ 查看版本号")
            print("2. 访问: https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/")
            print("3. 下载与你的 Edge 版本匹配的 WebDriver (选择 x64 版本)")
            print(f"4. 将下载的 msedgedriver.exe 文件放置到: {driver_path}")
            print("5. 重新运行此脚本")
            print("\n💡 或者运行以下命令尝试自动下载:")
            print("python download_edge_driver.py")
            return None

service = setup_edge_driver()
if service is None:
    print("\n❌ 无法设置 Edge WebDriver，程序退出")
    exit(1)

driver = webdriver.Edge(service=service, options=options)

try:
    # 访问网站
    driver.get("https://audiences.me/")
    print("成功访问 audiences.me")

    # 注入 Cookie
    cookies = parse_cookie(cookie_str)
    print(f"准备添加 {len(cookies)} 个 Cookie")

    for cookie in cookies:
        try:
            driver.add_cookie(cookie)
            print(f"成功添加 Cookie: {cookie['name']}")
        except Exception as e:
            print(f"添加 Cookie 失败: {cookie['name']}, 错误: {e}")

    # 刷新页面以应用 Cookie
    driver.refresh()
    print("页面已刷新，Cookie 已应用")

    # 保持浏览器打开，等待用户操作
    input("按 Enter 键关闭浏览器...")

except Exception as e:
    print(f"运行过程中出现错误: {e}")
finally:
    # 关闭浏览器
    driver.quit()
    print("浏览器已关闭")