from selenium import webdriver
# Playwright 已内置浏览器驱动，无需额外配置

# 你的 Cookie 字符串
cookie_str = "cf_clearance=BfAfNFUwX7oPVIw8D.PCC34AzK1PKA7CDUi45L8FkrM-1748527860-1.2.1.1-t9RSTYBx5NTG9ieEvMjFyPD8sZLp.DXyKjXyJaJtuCpPTQg_nN.VDeyYjndrS9eURRPog9MKnlL1d22yU6_02z9uBXKcQ_MkSNgWdrnQ7RGV4AJFQWdZDViFbDpHcBAnOPoOXHyhsCDgd__5Fl96n_jP9fuZu1ODlFb8pNsZHfRASv.ajLeHZQLurCQA60NUyxIs0QHADaHD35_.ED50gBfV5TuAzToU1hF6KnkFnIe465MbCtDOyWWKJcdup7EpenvT4Dic636YNDel9VT4FgwrtKO.VnS16JbeQkwnBYtjsNFu0vCjGhHZDUm4.vW3GKupWOcd5T2SaOSpWOQ9ResSsrB0QPzMgXga5hL5nOH.S2tgsvsQa1sCqq8yKIjN; c_secure_uid=MjE2NDc%3D; c_secure_pass=9e5a93ed444bd64e615d7213651e0bf0; c_secure_ssl=eWVhaA%3D%3D; c_secure_tracker_ssl=eWVhaA%3D%3D; c_secure_login=bm9wZQ%3D%3D"

def parse_cookie(cookie_str):
    cookies = []
    for item in cookie_str.split(';'):
        if '=' in item:
            name, value = item.strip().split('=', 1)
            cookies.append({'name': name, 'value': value, 'domain': 'audiences.me'})
    return cookies

# 启动 Edge 浏览器
# Playwright 使用 launch() 参数配置浏览器
# Playwright 通过 context 管理浏览器生命周期，无需此配置
try:
    service = Service(EdgeChromiumDriverManager().install())
except Exception as e:
    print(f"自动下载 Edge 驱动失败，请手动下载并放置到 drivers 目录下。错误: {e}")
    print("下载地址: https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/")
    service = Service(executable_path=r"c:\\Users\\<USER>\\Desktop\\br\\drivers\\msedgedriver.exe")
driver = webdriver.Edge(service=service, options=options)

driver.get("https://audiences.me/")

# 注入 Cookie
for cookie in parse_cookie(cookie_str):
    try:
        driver.add_cookie(cookie)
    except Exception as e:
        print(f"添加 Cookie 失败: {cookie}, 错误: {e}")

driver.refresh()