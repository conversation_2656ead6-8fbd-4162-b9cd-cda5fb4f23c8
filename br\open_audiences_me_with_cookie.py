from playwright.sync_api import sync_playwright
import json
import time

# 你的 Cookie 字符串
cookie_str = "cf_clearance=BfAfNFUwX7oPVIw8D.PCC34AzK1PKA7CDUi45L8FkrM-1748527860-1.2.1.1-t9RSTYBx5NTG9ieEvMjFyPD8sZLp.DXyKjXyJaJtuCpPTQg_nN.VDeyYjndrS9eURRPog9MKnlL1d22yU6_02z9uBXKcQ_MkSNgWdrnQ7RGV4AJFQWdZDViFbDpHcBAnOPoOXHyhsCDgd__5Fl96n_jP9fuZu1ODlFb8pNsZHfRASv.ajLeHZQLurCQA60NUyxIs0QHADaHD35_.ED50gBfV5TuAzToU1hF6KnkFnIe465MbCtDOyWWKJcdup7EpenvT4Dic636YNDel9VT4FgwrtKO.VnS16JbeQkwnBYtjsNFu0vCjGhHZDUm4.vW3GKupWOcd5T2SaOSpWOQ9ResSsrB0QPzMgXga5hL5nOH.S2tgsvsQa1sCqq8yKIjN; c_secure_uid=MjE2NDc%3D; c_secure_pass=9e5a93ed444bd64e615d7213651e0bf0; c_secure_ssl=eWVhaA%3D%3D; c_secure_tracker_ssl=eWVhaA%3D%3D; c_secure_login=bm9wZQ%3D%3D"

def parse_cookie_string_to_playwright_format(cookie_str, domain='audiences.me'):
    """将 Cookie 字符串转换为 Playwright 格式"""
    cookies = []
    for item in cookie_str.split(';'):
        if '=' in item:
            name, value = item.strip().split('=', 1)
            cookies.append({
                'name': name,
                'value': value,
                'domain': domain,
                'path': '/'
            })
    return cookies

def open_audiences_me_with_cookies():
    """使用 Playwright 打开 audiences.me 并注入 Cookie"""
    print("🚀 启动 Playwright 浏览器...")

    with sync_playwright() as p:
        # 启动 Edge 浏览器（Playwright 内置驱动，无需手动下载）
        browser = p.chromium.launch(
            headless=False,  # 设置为 False 以显示浏览器窗口
            slow_mo=1000,    # 操作间隔，便于观察
            args=[
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--no-sandbox'
            ]
        )

        # 创建浏览器上下文
        context = browser.new_context(
            viewport={'width': 1280, 'height': 720},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36'
        )

        # 创建新页面
        page = context.new_page()

        try:
            print("� 访问 audiences.me...")
            # 首先访问网站以建立域名上下文
            page.goto("https://audiences.me/index.php", wait_until='domcontentloaded', timeout=30000)
            print("✅ 成功访问 audiences.me")

            # 解析并注入 Cookie
            cookies = parse_cookie_string_to_playwright_format(cookie_str)
            print(f"🍪 准备注入 {len(cookies)} 个 Cookie...")

            # 注入 Cookie 到浏览器上下文
            context.add_cookies(cookies)
            print("✅ Cookie 注入成功")

            # 刷新页面以应用 Cookie
            print("🔄 刷新页面以应用 Cookie...")
            page.reload(wait_until='domcontentloaded')
            print("✅ 页面已刷新，Cookie 已应用")

            # 等待页面完全加载
            page.wait_for_timeout(3000)

            # 检查登录状态
            print("🔍 检查登录状态...")
            try:
                # 检查是否有登录相关的元素
                if page.locator('text=登录').count() > 0:
                    print("⚠️ 可能未成功登录，页面仍显示登录按钮")
                elif page.locator('text=用户').count() > 0 or page.locator('text=个人').count() > 0:
                    print("✅ 可能已成功登录，检测到用户相关元素")
                else:
                    print("ℹ️ 无法确定登录状态，请手动检查")
            except Exception as e:
                print(f"⚠️ 检查登录状态时出错: {e}")

            print("\n🎉 浏览器已打开并应用 Cookie")
            print("� 请在浏览器中检查登录状态")
            print("⏸️ 按 Enter 键关闭浏览器...")
            input()

        except Exception as e:
            print(f"❌ 操作过程中出现错误: {e}")

        finally:
            print("🔒 关闭浏览器...")
            browser.close()
            print("✅ 浏览器已关闭")

if __name__ == "__main__":
    print("🎯 Audiences.me Cookie 注入工具 (Playwright 版本)")
    print("=" * 50)
    open_audiences_me_with_cookies()