from playwright.sync_api import sync_playwright
import json
import time

# 你的 Cookie 字符串
cookie_str = "cf_clearance=BfAfNFUwX7oPVIw8D.PCC34AzK1PKA7CDUi45L8FkrM-1748527860-1.2.1.1-t9RSTYBx5NTG9ieEvMjFyPD8sZLp.DXyKjXyJaJtuCpPTQg_nN.VDeyYjndrS9eURRPog9MKnlL1d22yU6_02z9uBXKcQ_MkSNgWdrnQ7RGV4AJFQWdZDViFbDpHcBAnOPoOXHyhsCDgd__5Fl96n_jP9fuZu1ODlFb8pNsZHfRASv.ajLeHZQLurCQA60NUyxIs0QHADaHD35_.ED50gBfV5TuAzToU1hF6KnkFnIe465MbCtDOyWWKJcdup7EpenvT4Dic636YNDel9VT4FgwrtKO.VnS16JbeQkwnBYtjsNFu0vCjGhHZDUm4.vW3GKupWOcd5T2SaOSpWOQ9ResSsrB0QPzMgXga5hL5nOH.S2tgsvsQa1sCqq8yKIjN; c_secure_uid=MjE2NDc%3D; c_secure_pass=9e5a93ed444bd64e615d7213651e0bf0; c_secure_ssl=eWVhaA%3D%3D; c_secure_tracker_ssl=eWVhaA%3D%3D; c_secure_login=bm9wZQ%3D%3D"

def parse_cookie_string_to_playwright_format(cookie_str, domain='audiences.me'):
    """将 Cookie 字符串转换为 Playwright 格式"""
    cookies = []
    for item in cookie_str.split(';'):
        if '=' in item:
            name, value = item.strip().split('=', 1)
            cookies.append({
                'name': name,
                'value': value,
                'domain': domain,
                'path': '/'
            })
    return cookies

def open_audiences_me_with_cookies():
    """使用 Playwright 打开 audiences.me 并注入 Cookie"""
    print("🚀 启动 Playwright 浏览器...")

    with sync_playwright() as p:
        # 启动 Chromium 浏览器（Playwright 内置驱动，无需手动下载）
        browser = p.chromium.launch(
            headless=False,  # 设置为 False 以显示浏览器窗口
            slow_mo=500,     # 减少操作间隔，提高响应速度
            args=[
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--no-sandbox',
                '--start-maximized',  # 启动时最大化窗口
                '--disable-infobars',  # 禁用信息栏
                '--disable-extensions'  # 禁用扩展
            ]
        )

        # 创建浏览器上下文（设置为无视口限制以支持最大化）
        context = browser.new_context(
            viewport=None,  # 设置为 None 以使用系统默认大小（支持最大化）
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36'
        )

        # 创建新页面
        page = context.new_page()

        try:
            # 最大化浏览器窗口
            print("🖥️ 最大化浏览器窗口...")
            page.set_viewport_size({"width": 1920, "height": 1080})

            print("� 访问 audiences.me...")
            # 首先访问网站根目录以建立域名上下文
            page.goto("https://audiences.me/", wait_until='domcontentloaded', timeout=30000)
            print("✅ 成功访问 audiences.me")

            # 解析并注入 Cookie
            cookies = parse_cookie_string_to_playwright_format(cookie_str)
            print(f"🍪 准备注入 {len(cookies)} 个 Cookie...")

            # 注入 Cookie 到浏览器上下文
            context.add_cookies(cookies)
            print("✅ Cookie 注入成功")

            # 等待一下确保 Cookie 生效
            page.wait_for_timeout(1000)

            # 重新访问主页以应用 Cookie 并避免重定向到登录页
            print("🔄 重新访问主页以应用 Cookie...")
            page.goto("https://audiences.me/index.php", wait_until='domcontentloaded', timeout=30000)
            print("✅ 已访问主页，Cookie 已应用")

            # 等待页面完全加载
            page.wait_for_timeout(2000)

            # 检查登录状态和页面重定向
            print("🔍 检查登录状态和页面状态...")
            try:
                current_url = page.url
                print(f"📍 当前页面 URL: {current_url}")

                # 检查是否被重定向到登录页面
                if 'login.php' in current_url:
                    print("⚠️ 页面被重定向到登录页面，Cookie 可能无效或已过期")
                    print("🔄 尝试直接访问主页...")
                    page.goto("https://audiences.me/index.php", wait_until='domcontentloaded', timeout=30000)
                    page.wait_for_timeout(2000)

                    # 再次检查 URL
                    current_url = page.url
                    if 'login.php' in current_url:
                        print("❌ 仍然被重定向到登录页面，Cookie 无效")
                    else:
                        print("✅ 成功访问主页，Cookie 有效")
                elif 'index.php' in current_url or current_url.endswith('audiences.me/'):
                    print("✅ 成功停留在主页，Cookie 有效")
                else:
                    print(f"ℹ️ 当前在其他页面: {current_url}")

                # 检查页面内容以确认登录状态
                page_content = page.content()
                if 'login' in page_content.lower() and 'password' in page_content.lower():
                    print("⚠️ 页面包含登录表单，可能未成功登录")
                elif 'logout' in page_content.lower() or 'dashboard' in page_content.lower():
                    print("✅ 检测到登出链接或仪表板，已成功登录")
                else:
                    print("ℹ️ 无法从页面内容确定登录状态，请手动检查")

            except Exception as e:
                print(f"⚠️ 检查登录状态时出错: {e}")

            print("\n🎉 浏览器已打开并应用 Cookie")
            print("� 请在浏览器中检查登录状态")
            print("⏸️ 按 Enter 键关闭浏览器...")
            input()

        except Exception as e:
            print(f"❌ 操作过程中出现错误: {e}")

        finally:
            print("🔒 关闭浏览器...")
            browser.close()
            print("✅ 浏览器已关闭")

if __name__ == "__main__":
    print("🎯 Audiences.me Cookie 注入工具 (Playwright 版本)")
    print("=" * 50)
    open_audiences_me_with_cookies()