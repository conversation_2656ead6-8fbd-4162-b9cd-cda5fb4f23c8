import os
import json
import asyncio
from playwright.async_api import async_playwright, TimeoutError
import logging
from playwright.sync_api import sync_playwright, TimeoutError as PlaywrightTimeoutError
import random
import time
from typing import Optional, List
import multiprocessing
import sys
import concurrent.futures

# 导入MySQL工具模块
import mysql_utils
from mysql_utils import (
    save_cookie_to_db, get_cookie_from_db, 
    get_all_cookies_from_db, update_cookie_status_in_db
)

# 导入配置文件
from config import (
    ACCOUNT_CONFIG, FILE_CONFIG, LOG_CONFIG, LOGIN_CONFIG,
    BROWSER_CONFIG, URL_CONFIG, DEFAULT_TIMEOUT, NAVIGATION_TIMEOUT,
    MYSQL_CONFIG  # 添加MySQL配置导入
)

# 配置日志
# 如果需要详细日志，使用配置文件中的设置
ENABLE_DEBUG_LOG = LOG_CONFIG['enable_debug']

# 配置日志级别和格式
log_level = logging.INFO if not ENABLE_DEBUG_LOG else logging.DEBUG
logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

# 如果启用调试日志并且配置为保存到文件，添加文件处理程序
if ENABLE_DEBUG_LOG and LOG_CONFIG['save_to_file']:
    logging.basicConfig(
        handlers=[
            logging.FileHandler(FILE_CONFIG['log_file'], encoding='utf-8')
        ]
    )
    logging.info(f"已启用详细日志记录，日志将保存到{FILE_CONFIG['log_file']}文件")
else:
    logging.info("已启用简化日志记录，只输出到控制台")

# 从配置文件获取登录相关设置
MAX_RETRIES = LOGIN_CONFIG['max_retries']
# 使用从 config.py 导入的 DEFAULT_TIMEOUT 和 NAVIGATION_TIMEOUT
TARGET_URL = URL_CONFIG['rewards_url']  # 使用 rewards_url 替代硬编码 target_url
# 从配置文件获取账号和密码
EMAIL = ACCOUNT_CONFIG['email']
PASSWORD = ACCOUNT_CONFIG['password']

# 等待任意一个选择器出现
async def _wait_for_any_selector(page, selectors, timeout=10000):
    """尝试等待多个选择器中的任意一个出现"""
    for selector in selectors:
        try:
            logging.info(f"尝试查找选择器: {selector}")
            result = await page.wait_for_selector(selector, state='visible', timeout=timeout)
            if result:
                logging.info(f"找到选择器: {selector}")
                return result
        except Exception as e:
            logging.debug(f"选择器 {selector} 未找到: {str(e)}")
            continue
    
    logging.info("未找到任何匹配的选择器")
    return None

# JavaScript填充邮箱
async def _js_fill_email(page, email):
    """使用JavaScript填充邮箱字段"""
    logging.info("尝试使用JavaScript直接填充邮箱输入框")
    await page.evaluate(f'''
    const inputs = Array.from(document.querySelectorAll('input'));
    const emailInput = inputs.find(input => 
        input.type === 'email' || 
        input.name === 'loginfmt' || 
        (input.placeholder && (
            input.placeholder.includes('邮箱') || 
            input.placeholder.includes('电子邮件') ||
            input.placeholder.includes('email')
        )) ||
        (input.ariaLabel && input.ariaLabel.includes('电子邮件'))
    );
    if (emailInput) {{
        emailInput.value = "{email}";
        const event = new Event('change', {{ bubbles: true }});
        emailInput.dispatchEvent(event);
        console.log('Found and filled email input via JS');
        
        // 尝试提交表单
        const form = emailInput.closest('form');
        if (form) {{
            setTimeout(() => form.submit(), 500);
            console.log('Submitted form via JS');
        }}
    }} else {{
        console.log('Could not find email input via JS');
    }}
    ''')
    await page.wait_for_timeout(2000)
    await page.keyboard.press('Enter')

# JavaScript填充密码
async def _js_fill_password(page, password):
    """使用JavaScript填充密码字段"""
    logging.info("尝试使用JavaScript直接填充密码")
    await page.evaluate(f'''
    const inputs = Array.from(document.querySelectorAll('input'));
    const passwordInput = inputs.find(input => 
        input.type === 'password' || 
        input.name === 'passwd'
    );
    if (passwordInput) {{
        passwordInput.value = "{password}";
        const event = new Event('change', {{ bubbles: true }});
        passwordInput.dispatchEvent(event);
        console.log('Found and filled password input via JS');
        
        // 尝试提交表单
        const form = passwordInput.closest('form');
        if (form) {{
            setTimeout(() => form.submit(), 500);
            console.log('Submitted form via JS');
        }}
    }} else {{
        console.log('Could not find password input via JS');
    }}
    ''')
    await page.wait_for_timeout(2000)
    await page.keyboard.press('Enter')

async def save_cookies_as_text(context):
    """
    将cookies保存为文本串格式，确保获取所有必要域名的cookies
    """
    # 获取多个域名的 cookies - 扩展域名列表确保获取所有必要Cookie
    domains = [
        'https://www.bing.com',
        URL_CONFIG['rewards_url'],
        'https://account.microsoft.com',
        URL_CONFIG['login_url'],
        'https://account.live.com',
        'https://cn.bing.com',
        'https://www.microsoft.com',
        'https://edge.microsoft.com',
        'https://outlook.live.com',
        'https://office.com',
        'https://bing.com'
    ]
    
    # 获取所有域名的 cookies
    cookies = []
    for domain in domains:
        try:
            domain_cookies = await context.cookies([domain])
            cookies.extend(domain_cookies)
            logging.debug(f"成功获取域名 {domain} 的Cookies: {len(domain_cookies)}个")
        except Exception as e:
            logging.warning(f"获取域名 {domain} 的Cookies失败: {str(e)}")
    
    # 去重，避免重复的 cookies
    unique_cookies = []
    seen = set()
    for cookie in cookies:
        key = (cookie['name'], cookie['domain'])
        if key not in seen:
            seen.add(key)
            unique_cookies.append(cookie)
    
    # 将完整的cookie对象序列化为JSON
    cookie_json = json.dumps(unique_cookies)
    
    # 记录日志但不保存到文件
    if ENABLE_DEBUG_LOG:
        logging.info(f"已成功获取 {len(unique_cookies)} 个cookie")
        logging.info(f"保存的域名包括: {', '.join(set(c['domain'] for c in unique_cookies))}")
        logging.debug(f"Cookie JSON格式样例：{cookie_json[:100]}...")
    
    # 同时生成简单文本格式供兼容性使用
    cookie_strings = []
    for cookie in unique_cookies:
        cookie_strings.append(f"{cookie['name']}={cookie['value']}")
    cookie_text = '; '.join(cookie_strings)
    
    return cookie_text, unique_cookies

def save_cookies(cookies, filename=None):
    """
    保存cookies到文件和MySQL数据库
    
    如果传入的是playwright格式的cookies列表，将保存完整JSON和文本格式
    如果传入的是文本格式的cookie，直接保存
    """
    # 如果没有指定文件名，使用配置中的默认值
    if filename is None:
        filename = FILE_CONFIG['cookie_txt']
        
    try:
        account_id = ACCOUNT_CONFIG['email']  # 使用邮箱作为账号标识
        cookie_text = None
        cookie_json_str = None
        
        # 检查cookies格式并转换为字符串
        if isinstance(cookies, list) and cookies and isinstance(cookies[0], dict) and 'name' in cookies[0] and 'value' in cookies[0]:
            # 传入的是playwright格式的cookies列表，保存为JSON
            json_filename = FILE_CONFIG['cookie_json']
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=2)
            logging.info(f"完整Cookie对象已保存到 {json_filename}")
            
            # 同时保存文本格式
            cookie_text = '; '.join([f"{c['name']}={c['value']}" for c in cookies])
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(cookie_text)
            logging.info(f"Cookie文本格式已保存到 {filename}")
            
            # 保存到MySQL数据库
            cookie_json_str = json.dumps(cookies, ensure_ascii=False)
            
        elif isinstance(cookies, str):
            # 已经是字符串格式
            cookie_text = cookies
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(cookies)
            logging.info(f"Cookie字符串已保存到 {filename}")
        else:
            logging.error(f"无法识别的cookie格式: {type(cookies)}")
            return False
        
        # 输出Cookie详细信息
        if cookie_text:
            # 输出Cookie总长度
            logging.info(f"Cookie文本总长度: {len(cookie_text)} 字符")
            
            # 检查是否包含关键Cookie
            important_cookies = ['MUID', '_U', 'ANON', '_EDGE_V', '_EDGE_S', 'SRCHHPGUSR', 
                               'SRCHUID', 'SRCHD', 'USRLOC', '_SS', 'MUIDB',
                               'NAP', 'WLID', 'WLS', 'SUID', '_HPVN', '_RwBf']
            
            found_keys = []
            for key in important_cookies:
                if f"{key}=" in cookie_text:
                    found_keys.append(key)
            
            logging.info(f"找到的关键Cookie字段 ({len(found_keys)}/{len(important_cookies)}): {', '.join(found_keys)}")
            
            missing_keys = [key for key in important_cookies if key not in found_keys]
            if missing_keys:
                logging.warning(f"缺少的关键Cookie字段: {', '.join(missing_keys)}")
            
            # 保存到MySQL数据库
            try:
                # 尝试从cookie中提取用户名和积分信息
                account_name = None
                points = 0
                
                # 保存到数据库
                save_result = save_cookie_to_db(
                    account_id=account_id,
                    cookie_text=cookie_text,
                    cookie_json=cookie_json_str,
                    account_name=account_name,
                    points=points
                )
                
                if save_result:
                    logging.info(f"✅ Cookie已成功保存到MySQL数据库")
                    logging.info(f"├── 账号ID: {account_id}")
                    logging.info(f"├── 数据库主机: {MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}")
                    logging.info(f"├── 数据库名: {MYSQL_CONFIG['database']}")
                    logging.info(f"└── 表名: {MYSQL_CONFIG['table_name']}")
                    
                    # 尝试查询数据库验证保存是否成功
                    try:
                        from mysql_utils import get_cookie_from_db
                        saved_cookie = get_cookie_from_db(account_id)
                        if saved_cookie:
                            logging.info(f"✅ 数据库验证成功：成功从数据库读取Cookie")
                            logging.info(f"└── 数据库中Cookie长度: {len(saved_cookie.get('cookie_text', ''))}")
                        else:
                            logging.warning("⚠️ 数据库验证异常：无法从数据库读取刚保存的Cookie")
                    except Exception as e:
                        logging.warning(f"⚠️ 数据库验证时出错: {str(e)}")
                    
                    return True
                else:
                    logging.error("❌ 保存Cookie到MySQL数据库失败")
            except Exception as e:
                logging.error(f"❌ 保存Cookie到MySQL数据库时出错: {str(e)}")
        
        return True
    except Exception as e:
        logging.error(f"保存 cookies 时出错: {str(e)}")
        return False

def simple_login():
    """简化版登录过程，直接使用同步方式"""
    try:
        with sync_playwright() as p:
            try:
                browser = p.chromium.launch(
                    headless=BROWSER_CONFIG['headless'],
                    slow_mo=BROWSER_CONFIG['slow_mo'],
                    args=[
                        '--disable-gpu',
                        '--disable-web-security',
                        '--no-sandbox'
                    ]
                )
                
                # 创建新的浏览器上下文
                context = browser.new_context(
                    viewport={'width': BROWSER_CONFIG['viewport_width'], 'height': BROWSER_CONFIG['viewport_height']},
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36'
                )
                
                # 创建新页面
                page = context.new_page()
                
                try:
                    # 直接访问微软登录页面
                    logging.info("访问登录页面...")
                    page.goto(URL_CONFIG['login_url'], wait_until='domcontentloaded', timeout=LOGIN_CONFIG['navigation_timeout'])
                    page.wait_for_load_state('domcontentloaded')
                    
                    logging.info("等待页面加载完成...")
                    page.wait_for_timeout(5000)
                    
                    # 记录页面HTML以便调试
                    if ENABLE_DEBUG_LOG:
                        html_content = page.content()
                        logging.debug(f"页面HTML前200字符: {html_content[:200]}...")
                    
                    # 使用更通用的选择器来查找邮箱输入框
                    email_selectors = [
                        '#i0116',  # 标准选择器
                        'input[type="email"]',  # 基于属性的选择器
                        'input[name="loginfmt"]',  # 基于name属性的选择器
                        '[aria-label="输入电子邮件、电话或 Skype。"]',  # 基于aria-label的选择器
                        '[placeholder*="电子邮件"]',  # 基于占位文本的选择器
                        '[placeholder*="邮箱"]',
                        '[placeholder*="email"]',
                        'input.form-control'  # 基于类的选择器
                    ]
                    
                    found_email_input = False
                    logging.info("尝试查找邮箱输入框...")
                    
                    for selector in email_selectors:
                        try:
                            if page.locator(selector).count() > 0:
                                logging.info(f"找到邮箱输入框: {selector}")
                                page.fill(selector, EMAIL)
                                page.wait_for_timeout(1000)
                                found_email_input = True
                                
                                # 点击下一步
                                next_button_selectors = [
                                    '#idSIButton9',
                                    'input[type="submit"]',
                                    'button[type="submit"]',
                                    '[value="下一步"]',
                                    '[value="Next"]',
                                    'button.win-button'
                                ]
                                
                                next_clicked = False
                                for btn_selector in next_button_selectors:
                                    if page.locator(btn_selector).count() > 0:
                                        logging.info(f"点击下一步按钮: {btn_selector}")
                                        page.click(btn_selector)
                                        next_clicked = True
                                        break
                                
                                if not next_clicked:
                                    # 如果找不到按钮，尝试按回车键
                                    logging.info("未找到下一步按钮，尝试按回车键")
                                    page.keyboard.press('Enter')
                                
                                break
                        except Exception as e:
                            logging.debug(f"尝试选择器 {selector} 失败: {str(e)}")
                    
                    if not found_email_input:
                        logging.error("未找到任何可用的邮箱输入框")
                        # 尝试使用JavaScript注入
                        try:
                            logging.info("尝试使用JavaScript直接填充邮箱")
                            page.evaluate(f'''
                            const inputs = Array.from(document.querySelectorAll('input'));
                            const emailInput = inputs.find(input => 
                                input.type === 'email' || 
                                input.name === 'loginfmt' || 
                                (input.placeholder && (
                                    input.placeholder.includes('邮箱') || 
                                    input.placeholder.includes('电子邮件') ||
                                    input.placeholder.includes('email')
                                )) ||
                                (input.ariaLabel && input.ariaLabel.includes('电子邮件'))
                            );
                            if (emailInput) {{
                                emailInput.value = "{EMAIL}";
                                const event = new Event('change', {{ bubbles: true }});
                                emailInput.dispatchEvent(event);
                                console.log('Found and filled email input via JS');
                                
                                // 尝试提交表单
                                const form = emailInput.closest('form');
                                if (form) {{
                                    setTimeout(() => form.submit(), 500);
                                    console.log('Submitted form via JS');
                                }}
                            }} else {{
                                console.log('Could not find email input via JS');
                            }}
                            ''')
                            
                            # 检查是否需要按回车键
                            page.wait_for_timeout(2000)
                            page.keyboard.press('Enter')
                            logging.info("尝试通过JS和回车键提交邮箱")
                        except Exception as e:
                            logging.error(f"JavaScript填充邮箱失败: {str(e)}")
                    
                    # 等待密码页面加载
                    logging.info("等待密码页面加载...")
                    page.wait_for_timeout(8000)
                    
                    # 检查是否成功前往密码输入页面
                    if ENABLE_DEBUG_LOG:
                        current_url = page.url
                        logging.debug(f"当前URL: {current_url}")
                    
                    # 使用更多选择器尝试找到密码输入框
                    password_selectors = [
                        '#i0118',  # 标准选择器
                        'input[type="password"]',  # 基于属性的选择器
                        'input[name="passwd"]',  # 基于name属性的选择器
                        '[placeholder*="密码"]',  # 基于占位文本的选择器
                        '[placeholder*="password"]',
                        '[aria-label*="密码"]'
                    ]
                    
                    found_password_input = False
                    logging.info("尝试查找密码输入框...")
                    
                    for selector in password_selectors:
                        try:
                            if page.locator(selector).count() > 0:
                                logging.info(f"找到密码输入框: {selector}")
                                page.fill(selector, PASSWORD)
                                page.wait_for_timeout(1000)
                                found_password_input = True
                                
                                # 点击登录
                                sign_in_selectors = [
                                    '#idSIButton9',
                                    'input[type="submit"]',
                                    'button[type="submit"]',
                                    '[value="登录"]',
                                    '[value="Sign in"]',
                                    'button.win-button'
                                ]
                                
                                sign_in_clicked = False
                                for btn_selector in sign_in_selectors:
                                    if page.locator(btn_selector).count() > 0:
                                        logging.info(f"点击登录按钮: {btn_selector}")
                                        page.click(btn_selector)
                                        sign_in_clicked = True
                                        break
                                
                                if not sign_in_clicked:
                                    # 如果找不到按钮，尝试按回车键
                                    logging.info("未找到登录按钮，尝试按回车键")
                                    page.keyboard.press('Enter')
                                
                                break
                        except Exception as e:
                            logging.debug(f"尝试选择器 {selector} 失败: {str(e)}")
                    
                    if not found_password_input:
                        logging.error("未找到任何可用的密码输入框")
                        # 尝试使用JavaScript注入
                        try:
                            logging.info("尝试使用JavaScript直接填充密码")
                            page.evaluate(f'''
                            const inputs = Array.from(document.querySelectorAll('input'));
                            const passwordInput = inputs.find(input => 
                                input.type === 'password' || 
                                input.name === 'passwd'
                            );
                            if (passwordInput) {{
                                passwordInput.value = "{PASSWORD}";
                                const event = new Event('change', {{ bubbles: true }});
                                passwordInput.dispatchEvent(event);
                                console.log('Found and filled password input via JS');
                                
                                // 尝试提交表单
                                const form = passwordInput.closest('form');
                                if (form) {{
                                    setTimeout(() => form.submit(), 500);
                                    console.log('Submitted form via JS');
                                }}
                            }} else {{
                                console.log('Could not find password input via JS');
                            }}
                            ''')
                            
                            # 检查是否需要按回车键
                            page.wait_for_timeout(2000)
                            page.keyboard.press('Enter')
                            logging.info("尝试通过JS和回车键提交密码")
                        except Exception as e:
                            logging.error(f"JavaScript填充密码失败: {str(e)}")
                    
                    # 等待登录完成
                    logging.info("等待登录完成...")
                    page.wait_for_timeout(8000)
                    
                    # 检查是否需要确认"保持登录"
                    stay_logged_in_selectors = [
                        '#idSIButton9',
                        'button:has-text("是")',
                        'button:has-text("Yes")',
                        '[value="是"]',
                        '[value="Yes"]'
                    ]
                    
                    for selector in stay_logged_in_selectors:
                        try:
                            if page.locator(selector).count() > 0 and (
                                page.content().find("保持登录") > -1 or 
                                page.content().find("Stay signed in") > -1
                            ):
                                logging.info(f"点击'保持登录'按钮: {selector}")
                                page.click(selector)
                                page.wait_for_timeout(5000)
                                break
                        except Exception as e:
                            logging.debug(f"尝试点击'保持登录'按钮 {selector} 失败: {str(e)}")
                    
                    # 访问 Rewards 页面
                    logging.info("访问 Rewards 页面...")
                    page.goto(URL_CONFIG['rewards_url'], wait_until='domcontentloaded', timeout=LOGIN_CONFIG['navigation_timeout'])
                    page.wait_for_timeout(10000)
                    
                    # 获取并保存完整的Cookie
                    logging.info("获取Rewards页面的完整Cookie...")
                    all_cookies = context.cookies()
                    save_cookies(all_cookies)
                    logging.info("登录成功，已保存完整Rewards页面的cookies")
                    
                    # 尝试获取用户信息
                    user_info = {}
                    
                    # 获取积分信息
                    try:
                        # 尝试获取可用积分
                        available_points = page.evaluate('''
                            () => {
                                const pointsElem = document.querySelector('.pointsValue');
                                return pointsElem ? pointsElem.textContent.trim() : null;
                            }
                        ''')
                        if available_points:
                            user_info['可用积分'] = available_points
                            logging.info(f"可用积分: {available_points}")

                        # 尝试获取用户等级
                        user_level = page.evaluate('''
                            () => {
                                const levelElem = document.querySelector('.profileDescription.level');
                                return levelElem ? levelElem.textContent.trim() : null;
                            }
                        ''')
                        if user_level:
                            user_info['用户等级'] = user_level
                            logging.info(f"用户等级: {user_level}")
                        
                        # 尝试获取今日积分
                        daily_points = page.evaluate('''
                            () => {
                                const dailyPoints = Array.from(document.querySelectorAll('.pointsValue')).find(
                                    el => el.closest('.info')?.querySelector('.pointsDescription')?.textContent.includes('今日积分')
                                );
                                return dailyPoints ? dailyPoints.textContent.trim() : null;
                            }
                        ''')
                        if daily_points:
                            user_info['今日积分'] = daily_points
                            logging.info(f"今日积分: {daily_points}")
                        
                        # 尝试获取连续计数
                        streak_count = page.evaluate('''
                            () => {
                                const streakCountEl = Array.from(document.querySelectorAll('.pointsValue')).find(
                                    el => el.closest('.info')?.querySelector('.pointsDescription')?.textContent.includes('连续计数')
                                );
                                return streakCountEl ? streakCountEl.textContent.trim() : null;
                            }
                        ''')
                        if streak_count:
                            user_info['连续计数'] = streak_count
                            logging.info(f"连续计数: {streak_count}")
                        
                        # 尝试获取连续保护信息
                        streak_protection = page.evaluate('''
                            () => {
                                // 检查连续保护是否开启
                                const protectionToggle = document.querySelector('.streakProtectionMode .toggleOn');
                                const protectionState = protectionToggle ? "已开启" : "未开启";
                                
                                // 尝试获取连续保护提示信息
                                const protectionInfo = document.querySelector('#streakProtectionTooltip-body');
                                const protectionText = protectionInfo ? protectionInfo.textContent.trim() : "";
                                
                                return {state: protectionState, info: protectionText};
                            }
                        ''')
                        if streak_protection:
                            user_info['连续保护状态'] = streak_protection['state']
                            if streak_protection['info']:
                                user_info['连续保护信息'] = streak_protection['info']
                            logging.info(f"连续保护: {streak_protection['state']} - {streak_protection['info']}")
                        
                        # 尝试获取个人信息（用户名等）
                        user_name = page.evaluate('''
                            () => {
                                const nameEl = document.querySelector('.persona h1.c-heading-2');
                                return nameEl ? nameEl.textContent.trim() : null;
                            }
                        ''')
                        if user_name:
                            user_info['用户名'] = user_name
                            logging.info(f"用户名: {user_name}")
                            
                        # 输出汇总信息
                        if user_info:
                            logging.info("=============== 用户信息汇总 ===============")
                            for key, value in user_info.items():
                                logging.info(f"{key}: {value}")
                            logging.info("=========================================")
                            
                            # 更新数据库中的用户信息
                            try:
                                points_value = 0
                                if available_points and available_points.isdigit():
                                    points_value = int(available_points)
                                    
                                # 尝试更新数据库中的积分和账号名
                                from mysql_utils import update_cookie_status_in_db
                                update_result = update_cookie_status_in_db(
                                    account_id=ACCOUNT_CONFIG['email'],
                                    points=points_value,
                                    is_valid=True
                                )
                                
                                if update_result:
                                    logging.info("✅ 成功更新数据库中的用户信息")
                                    logging.info(f"├── 账号: {ACCOUNT_CONFIG['email']}")
                                    logging.info(f"├── 用户名: {user_name}")
                                    logging.info(f"└── 积分: {points_value}")
                                else:
                                    logging.warning("⚠️ 更新数据库中的用户信息失败")
                            except Exception as e:
                                logging.warning(f"⚠️ 更新数据库用户信息时出错: {str(e)}")
                    except Exception as e:
                        logging.error(f"获取用户信息时出错: {str(e)}")
                    
                    # 最终验证
                    logging.info("\n========== 执行完成汇总 ==========")
                    try:
                        # 验证Cookie是否成功保存到数据库
                        from mysql_utils import get_cookie_from_db
                        db_cookie = get_cookie_from_db(ACCOUNT_CONFIG['email'])
                        if db_cookie:
                            cookie_text = db_cookie.get('cookie_text', '')
                            cookie_length = len(cookie_text)
                            last_update = db_cookie.get('updated_at', '未知')
                            is_valid = db_cookie.get('is_valid', False)
                            
                            logging.info("📊 数据库Cookie状态:")
                            logging.info(f"├── 账号: {ACCOUNT_CONFIG['email']}")
                            logging.info(f"├── Cookie长度: {cookie_length} 字符")
                            logging.info(f"├── 有效状态: {'有效' if is_valid else '无效'}")
                            logging.info(f"├── 最后更新: {last_update}")
                            
                            # 检查关键字段
                            if cookie_text:
                                important_cookies = ['MUID', '_U', 'ANON', '_EDGE_V', '_EDGE_S', 'SRCHHPGUSR']
                                found_keys = [key for key in important_cookies if f"{key}=" in cookie_text]
                                logging.info(f"└── 关键字段: {len(found_keys)}/{len(important_cookies)} - {', '.join(found_keys)}")
                        else:
                            logging.warning("⚠️ 无法从数据库中读取Cookie，请检查保存过程")
                    except Exception as e:
                        logging.warning(f"⚠️ 验证数据库Cookie时出错: {str(e)}")
                    
                    logging.info("\n🎉 自动登录和Cookie获取已完成")
                    logging.info("📝 可以运行 bing.py 执行搜索任务")
                    logging.info("================================")
                
                except Exception as e:
                    logging.error(f"登录过程中出错: {str(e)}")
                    raise
                
                finally:
                    browser.close()
                    
            except Exception as e:
                logging.error(f"无法启动浏览器: {str(e)}")
                raise
                
    except Exception as e:
        logging.error(f"登录失败: {str(e)}")
        return False
        
    return True

if __name__ == "__main__":
    # 直接使用简化版登录
    simple_login() 