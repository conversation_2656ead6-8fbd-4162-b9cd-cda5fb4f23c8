#!/usr/bin/env python3
"""
Edge WebDriver 下载工具
自动检测 Edge 版本并下载对应的 WebDriver
"""

import os
import sys
import subprocess
import requests
import zipfile
import tempfile
from pathlib import Path

def get_edge_version():
    """获取本地 Edge 浏览器版本"""
    try:
        # Windows 注册表方法
        result = subprocess.run([
            'reg', 'query', 
            'HKEY_CURRENT_USER\\Software\\Microsoft\\Edge\\BLBeacon', 
            '/v', 'version'
        ], capture_output=True, text=True, check=True)
        
        for line in result.stdout.split('\n'):
            if 'version' in line and 'REG_SZ' in line:
                version = line.split()[-1]
                return version
    except:
        pass
    
    # 备用方法：尝试从程序文件夹获取
    try:
        edge_paths = [
            r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
            r"C:\Program Files\Microsoft\Edge\Application\msedge.exe"
        ]
        
        for path in edge_paths:
            if os.path.exists(path):
                result = subprocess.run([
                    'powershell', '-Command',
                    f'(Get-ItemProperty "{path}").VersionInfo.FileVersion'
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    return result.stdout.strip()
    except:
        pass
    
    return None

def download_edge_driver(version, target_dir):
    """下载指定版本的 Edge WebDriver"""
    # 构建下载 URL
    major_version = version.split('.')[0]
    download_url = f"https://msedgedriver.azureedge.net/{version}/edgedriver_win64.zip"
    
    print(f"正在下载 Edge WebDriver {version}...")
    print(f"下载地址: {download_url}")
    
    try:
        # 下载文件
        response = requests.get(download_url, stream=True)
        response.raise_for_status()
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.zip') as temp_file:
            for chunk in response.iter_content(chunk_size=8192):
                temp_file.write(chunk)
            temp_zip_path = temp_file.name
        
        # 解压文件
        with zipfile.ZipFile(temp_zip_path, 'r') as zip_ref:
            # 查找 msedgedriver.exe
            for file_info in zip_ref.filelist:
                if file_info.filename.endswith('msedgedriver.exe'):
                    # 提取到目标目录
                    zip_ref.extract(file_info, target_dir)
                    
                    # 移动到正确位置
                    extracted_path = os.path.join(target_dir, file_info.filename)
                    final_path = os.path.join(target_dir, 'msedgedriver.exe')
                    
                    if extracted_path != final_path:
                        os.rename(extracted_path, final_path)
                    
                    print(f"✅ Edge WebDriver 下载成功: {final_path}")
                    return True
        
        print("❌ 在下载的文件中未找到 msedgedriver.exe")
        return False
        
    except requests.RequestException as e:
        print(f"❌ 下载失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 处理文件时出错: {e}")
        return False
    finally:
        # 清理临时文件
        if 'temp_zip_path' in locals() and os.path.exists(temp_zip_path):
            os.unlink(temp_zip_path)

def main():
    """主函数"""
    print("🔍 Edge WebDriver 自动下载工具")
    print("=" * 40)
    
    # 获取 Edge 版本
    edge_version = get_edge_version()
    if not edge_version:
        print("❌ 无法检测到 Edge 浏览器版本")
        print("请确保已安装 Microsoft Edge 浏览器")
        return 1
    
    print(f"📋 检测到 Edge 版本: {edge_version}")
    
    # 确定目标目录
    script_dir = Path(__file__).parent
    drivers_dir = script_dir / "br" / "drivers"
    drivers_dir.mkdir(parents=True, exist_ok=True)
    
    driver_path = drivers_dir / "msedgedriver.exe"
    
    # 检查是否已存在
    if driver_path.exists():
        print(f"📁 发现现有驱动: {driver_path}")
        choice = input("是否重新下载？(y/N): ").lower()
        if choice not in ['y', 'yes']:
            print("✅ 使用现有驱动")
            return 0
    
    # 下载驱动
    if download_edge_driver(edge_version, str(drivers_dir)):
        print("🎉 Edge WebDriver 设置完成！")
        print(f"📂 驱动位置: {driver_path}")
        print("\n现在可以运行主脚本了:")
        print("python br/open_audiences_me_with_cookie.py")
        return 0
    else:
        print("❌ 自动下载失败")
        print("\n请手动下载:")
        print(f"1. 访问: https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/")
        print(f"2. 下载版本 {edge_version} 的 WebDriver")
        print(f"3. 将 msedgedriver.exe 放置到: {drivers_dir}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
