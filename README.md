# Audiences.me 自动登录工具

这个工具可以使用预设的 Cookie 自动登录到 audiences.me 网站。

## 环境要求

- Python 3.7+
- Microsoft Edge 浏览器
- Edge WebDriver

## 安装依赖

```bash
pip install -r requirements.txt
```

## Edge WebDriver 设置

### 自动下载（推荐）
脚本会尝试自动下载匹配的 Edge WebDriver。如果网络连接正常，无需手动操作。

### 手动下载
如果自动下载失败，请按以下步骤手动下载：

1. **检查 Edge 版本**：
   - 打开 Edge 浏览器
   - 访问 `edge://version/`
   - 记录版本号（当前检测到的版本：138.0.3351.121）

2. **下载对应版本的 WebDriver**：
   - 访问：https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/
   - 下载与你的 Edge 版本匹配的 WebDriver
   - 选择 x64 版本（适用于 64 位系统）

3. **放置驱动文件**：
   - 将下载的 `msedgedriver.exe` 文件放置到 `br/drivers/` 目录下
   - 确保文件路径为：`br/drivers/msedgedriver.exe`

## 使用方法

1. **更新 Cookie**（如需要）：
   - 编辑 `br/open_audiences_me_with_cookie.py` 文件
   - 更新 `cookie_str` 变量中的 Cookie 值

2. **运行脚本**：
   ```bash
   python br/open_audiences_me_with_cookie.py
   ```

3. **操作流程**：
   - 脚本会自动打开 Edge 浏览器
   - 访问 audiences.me 网站
   - 自动注入 Cookie
   - 刷新页面以应用登录状态
   - 浏览器保持打开状态，等待用户操作
   - 按 Enter 键关闭浏览器

## 故障排除

### 常见错误及解决方案

1. **"name 'Service' is not defined"**
   - 已修复：确保导入了正确的模块

2. **"自动下载 Edge 驱动失败"**
   - 检查网络连接
   - 按照上述手动下载步骤操作

3. **"本地驱动不存在"**
   - 确保 `msedgedriver.exe` 文件在正确位置
   - 检查文件权限

4. **Cookie 添加失败**
   - 检查 Cookie 格式是否正确
   - 确保域名匹配

## 文件结构

```
br/
├── br/
│   ├── open_audiences_me_with_cookie.py  # 主脚本
│   └── drivers/                          # WebDriver 目录
│       └── msedgedriver.exe             # Edge WebDriver（需手动下载）
├── requirements.txt                      # Python 依赖
└── README.md                            # 使用说明
```

## 注意事项

- Cookie 有时效性，过期后需要重新获取
- 确保 Edge 浏览器版本与 WebDriver 版本匹配
- 脚本运行时请勿关闭命令行窗口
- 建议定期更新 WebDriver 以匹配浏览器更新

## 维护者

- alxxxxla
