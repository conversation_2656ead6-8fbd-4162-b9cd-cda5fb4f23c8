# Audiences.me 自动登录工具 (Playwright 版本)

这个工具使用 Playwright 浏览器自动化框架，可以使用预设的 Cookie 自动登录到 audiences.me 网站。

## 环境要求

- Python 3.7+
- Playwright（内置浏览器驱动，无需手动下载）

## 安装依赖

```bash
pip install -r requirements.txt
```

## Playwright 浏览器设置

Playwright 内置了浏览器驱动，无需手动下载和管理 WebDriver。首次使用时会自动下载所需的浏览器：

```bash
# 安装 Chromium 浏览器（推荐）
playwright install chromium

# 或者安装所有浏览器
playwright install
```

## 功能特性

### 核心功能
- 🍪 **智能 Cookie 注入**：自动解析和注入 Cookie 到浏览器
- 🖥️ **自动窗口最大化**：启动时自动最大化浏览器窗口
- 🔄 **智能重定向处理**：自动检测并处理登录页面重定向
- 📍 **登录状态检测**：多重检查确认登录状态
- ⚡ **快速响应**：优化的操作间隔和页面加载

### 技术优势
相比 Selenium + WebDriver 方式，Playwright 具有以下优势：

- ✅ **无需手动管理驱动**：内置浏览器驱动，自动更新
- ✅ **更稳定**：更好的元素等待和页面加载处理
- ✅ **更快速**：启动速度更快，操作更流畅
- ✅ **更现代**：支持最新的 Web 标准和特性
- ✅ **跨平台**：Windows、macOS、Linux 统一体验

## 使用方法

1. **更新 Cookie**（如需要）：
   - 编辑 `br/open_audiences_me_with_cookie.py` 文件
   - 更新 `cookie_str` 变量中的 Cookie 值

2. **运行脚本**：
   ```bash
   python br/open_audiences_me_with_cookie.py
   ```

3. **操作流程**：
   - 脚本会自动启动 Chromium 浏览器（最大化窗口）
   - 访问 audiences.me 网站根目录
   - 自动注入 Cookie
   - 重新访问主页以应用登录状态
   - 智能检查登录状态和页面重定向
   - 自动处理登录页面重定向问题
   - 浏览器保持打开状态，等待用户操作
   - 按 Enter 键关闭浏览器

## 故障排除

### 常见错误及解决方案

1. **"playwright not found"**
   - 运行：`pip install playwright`
   - 然后运行：`playwright install chromium`

2. **浏览器启动失败**
   - 确保已安装浏览器：`playwright install chromium`
   - 检查系统权限和防火墙设置

3. **Cookie 注入失败**
   - 检查 Cookie 格式是否正确
   - 确保域名匹配（默认为 audiences.me）
   - 检查 Cookie 是否过期

4. **网络连接问题**
   - 检查网络连接
   - 确保可以访问 audiences.me

5. **页面重定向到登录页**
   - 脚本会自动检测并尝试重新访问主页
   - 如果仍然重定向，说明 Cookie 已过期，需要重新获取

6. **浏览器窗口无法最大化**
   - 脚本已自动配置窗口最大化
   - 如果仍有问题，检查系统显示设置

## 文件结构

```
br/
├── br/
│   └── open_audiences_me_with_cookie.py  # 主脚本（Playwright 版本）
├── requirements.txt                      # Python 依赖
├── download_edge_driver.py              # 旧版 Selenium 驱动下载工具（已弃用）
└── README.md                            # 使用说明
```

## 注意事项

- Cookie 有时效性，过期后需要重新获取
- Playwright 会自动管理浏览器版本，无需手动更新
- 脚本运行时请勿关闭命令行窗口
- 首次运行可能需要下载浏览器，请耐心等待

## 版本历史

- **v2.0** (当前版本): 使用 Playwright，无需手动管理驱动
- **v1.0** (已弃用): 使用 Selenium + WebDriver，需要手动下载驱动

## 维护者

- alxxxxla
